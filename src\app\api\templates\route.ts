import { NextRequest, NextResponse } from 'next/server';
import {
  loadTemplates,
  addTemplate,
  cleanWordHtml,
  extractPlaceholders,
  generateTemplateFilename,
  saveTemplateFile,
  updateImagePaths
} from '@/lib/templates';

export async function GET() {
  try {
    const templates = await loadTemplates();
    return NextResponse.json(templates);
  } catch (error) {
    console.error('Error loading templates:', error);
    return NextResponse.json(
      { error: 'Failed to load templates' },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { name, description, layoutSize, htmlContent, hasFolder } = body;

    // Validate input
    if (!name || !description || !htmlContent || !layoutSize) {
      return NextResponse.json(
        { error: 'Name, description, layout size, and HTML content are required' },
        { status: 400 }
      );
    }

    // Validate layout size
    if (layoutSize !== 'A4' && layoutSize !== 'Letter') {
      return NextResponse.json(
        { error: 'Layout size must be either A4 or Letter' },
        { status: 400 }
      );
    }

    // Generate unique filename and ID first
    const filename = generateTemplateFilename(name);
    const id = filename.replace('.html', '');

    // Clean the HTML content and update image paths if folder is uploaded
    let cleanedHtml = cleanWordHtml(htmlContent);

    if (hasFolder) {
      // Update image src paths to point to the uploaded folder
      cleanedHtml = updateImagePaths(cleanedHtml, id);
    }

    // Extract placeholders
    const placeholders = extractPlaceholders(cleanedHtml);
    
    // Save the HTML file
    await saveTemplateFile(filename, cleanedHtml);
    
    // Add template to templates.json
    await addTemplate({
      id,
      name: name.trim(),
      description: description.trim(),
      filename,
      placeholders,
      layoutSize,
    });

    return NextResponse.json({
      success: true,
      template: {
        id,
        name: name.trim(),
        description: description.trim(),
        filename,
        placeholders,
        layoutSize,
      }
    });
  } catch (error) {
    console.error('Error creating template:', error);
    return NextResponse.json(
      { error: 'Failed to create template' },
      { status: 500 }
    );
  }
}
