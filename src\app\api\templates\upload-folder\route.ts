import { NextRequest, NextResponse } from 'next/server';
import { promises as fs } from 'fs';
import path from 'path';

export async function POST(request: NextRequest) {
  try {
    const formData = await request.formData();
    const templateId = formData.get('templateId') as string;
    const files = formData.getAll('files') as File[];
    
    if (!templateId) {
      return NextResponse.json(
        { error: 'Template ID is required' },
        { status: 400 }
      );
    }

    if (files.length === 0) {
      return NextResponse.json(
        { error: 'No files provided' },
        { status: 400 }
      );
    }

    // Create the folder path in public directory
    const folderPath = path.join(process.cwd(), 'public', templateId);
    
    // Ensure the folder exists
    try {
      await fs.mkdir(folderPath, { recursive: true });
    } catch (error) {
      console.error('Error creating folder:', error);
      return NextResponse.json(
        { error: 'Failed to create folder' },
        { status: 500 }
      );
    }

    // Save all files to the folder
    const savedFiles = [];
    for (const file of files) {
      try {
        const arrayBuffer = await file.arrayBuffer();
        const buffer = Buffer.from(arrayBuffer);
        
        // Get the relative path from the file's webkitRelativePath
        const relativePath = (file as any).webkitRelativePath || file.name;
        const filePath = path.join(folderPath, relativePath);
        
        // Ensure the directory exists for nested files
        const fileDir = path.dirname(filePath);
        await fs.mkdir(fileDir, { recursive: true });
        
        // Write the file
        await fs.writeFile(filePath, buffer);
        
        savedFiles.push({
          name: file.name,
          path: relativePath,
          size: file.size
        });
      } catch (error) {
        console.error(`Error saving file ${file.name}:`, error);
        return NextResponse.json(
          { error: `Failed to save file ${file.name}` },
          { status: 500 }
        );
      }
    }

    return NextResponse.json({
      success: true,
      message: `Successfully uploaded ${savedFiles.length} files`,
      files: savedFiles,
      folderPath: `/${templateId}`
    });
  } catch (error) {
    console.error('Error uploading folder:', error);
    return NextResponse.json(
      { error: 'Failed to upload folder' },
      { status: 500 }
    );
  }
}
